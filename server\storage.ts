import {
  clients,
  loans,
  pointOfSales,
  requests,
  transactions,
  users,
  type Client,
  type InsertClient,
  type InsertUser,
  type Loan,
  type PointOfSale,
  type Request,
  type Transaction,
  type User
} from "@shared/schema";
import { count, desc, eq, sum } from "drizzle-orm";
import { drizzle } from "drizzle-orm/node-postgres";
import pg from "pg";

// Database connection setup
const { Pool } = pg;

// Create connection pool
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'backofficemanager',
  user: 'postgres',
  password: 'DhiaAdmin',
  ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
});

// Create Drizzle instance
export const db = drizzle(pool);

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getClients(): Promise<Client[]>;
  getClient(id: number): Promise<Client | undefined>;
  createClient(client: InsertClient): Promise<Client>;
  getRequests(): Promise<Request[]>;
  getRequest(id: number): Promise<Request | undefined>;
  getRequestStats(): Promise<{ title: string; value: string; icon: string; color: string; }[]>;
  getTransactions(clientId?: number): Promise<Transaction[]>;
  getLoans(clientId?: number): Promise<Loan[]>;
  getPointOfSales(clientId?: number): Promise<PointOfSale[]>;
  getDashboardStats(): Promise<{ title: string; value: string; change: string; trend: string; icon: string; color: string; }[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  currentId: number;

  constructor() {
    this.users = new Map();
    this.currentId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getClients(): Promise<Client[]> {
    return await db.select().from(clients);
  }

  async getClient(id: number): Promise<Client | undefined> {
    const result = await db.select().from(clients).where(eq(clients.id, id)).limit(1);
    return result[0];
  }

  async createClient(insertClient: InsertClient): Promise<Client> {
    const result = await db.insert(clients).values(insertClient).returning();
    return result[0];
  }

  async getRequests(): Promise<Request[]> {
    return await db.select().from(requests).orderBy(desc(requests.id));
  }

  async getRequest(id: number): Promise<Request | undefined> {
    const result = await db.select().from(requests).where(eq(requests.id, id)).limit(1);
    return result[0];
  }

  async getRequestStats(): Promise<{ title: string; value: string; icon: string; color: string; }[]> {
    const [underReview] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Under Review'));
    const [approved] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Approved'));
    const [rejected] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Rejected'));

    return [
      {
        title: "Under Review",
        value: underReview?.count?.toString() || "0",
        icon: "Play",
        color: "amber",
      },
      {
        title: "Approved",
        value: approved?.count?.toString() || "0",
        icon: "Check",
        color: "green",
      },
      {
        title: "Rejected",
        value: rejected?.count?.toString() || "0",
        icon: "AlertTriangle",
        color: "red",
      },
    ];
  }

  async getTransactions(clientId?: number): Promise<Transaction[]> {
    if (clientId) {
      return await db.select().from(transactions).where(eq(transactions.client_id, clientId)).orderBy(desc(transactions.date));
    }
    return await db.select().from(transactions).orderBy(desc(transactions.date));
  }

  async getLoans(clientId?: number): Promise<Loan[]> {
    if (clientId) {
      return await db.select().from(loans).where(eq(loans.client_id, clientId)).orderBy(desc(loans.date));
    }
    return await db.select().from(loans).orderBy(desc(loans.date));
  }

  async getPointOfSales(clientId?: number): Promise<PointOfSale[]> {
    if (clientId) {
      return await db.select().from(pointOfSales).where(eq(pointOfSales.client_id, clientId)).orderBy(desc(pointOfSales.date));
    }
    return await db.select().from(pointOfSales).orderBy(desc(pointOfSales.date));
  }

  async getDashboardStats(): Promise<{ title: string; value: string; change: string; trend: string; icon: string; color: string; }[]> {
    const [totalClients] = await db.select({ count: count() }).from(clients);
    const [activeRequests] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Under Review'));
    const [approvedRequests] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Approved'));
    const [financeGiven] = await db.select({ totalAmount: sum(requests.amount) }).from(requests).where(eq(requests.status, 'Approved'));


    return [
      {
        title: "Total Clients",
        value: totalClients?.count?.toString() || "0",
        change: "+12%",
        trend: "up",
        icon: "Users",
        color: "blue",
      },
      {
        title: "Active Requests",
        value: activeRequests?.count?.toString() || "0",
        change: "-3%",
        trend: "down",
        icon: "ClipboardList",
        color: "amber",
      },
      {
        title: "Approved Requests",
        value: approvedRequests?.count?.toString() || "0",
        change: "+8%",
        trend: "up",
        icon: "CheckCircle",
        color: "green",
      },
      {
        title: "Finance Given",
        value: (financeGiven?.totalAmount?.toString() || "0") + " KWD",
        change: "+15%",
        trend: "up",
        icon: "DollarSign",
        color: "purple",
      },
    ];
  }
}

export class PostgreSQLStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await db.insert(users).values(insertUser).returning();
    return result[0];
  }

  async getClients(): Promise<Client[]> {
    return await db.select().from(clients);
  }

  async getClient(id: number): Promise<Client | undefined> {
    const result = await db.select().from(clients).where(eq(clients.id, id)).limit(1);
    return result[0];
  }

  async createClient(insertClient: InsertClient): Promise<Client> {
    const result = await db.insert(clients).values(insertClient).returning();
    return result[0];
  }

  async getRequests(): Promise<Request[]> {
    return await db.select().from(requests).orderBy(desc(requests.created_at));
  }

  async getRequest(id: number): Promise<Request | undefined> {
    const result = await db.select().from(requests).where(eq(requests.id, id)).limit(1);
    return result[0];
  }

  async getRequestStats(): Promise<{ title: string; value: string; icon: string; color: string; }[]> {
    const [underReview] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Under Review'));
    const [approved] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Approved'));
    const [rejected] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'rejected'));

    return [
      {
        title: "Under Review",
        value: underReview?.count?.toString() || "0",
        icon: "Play",
        color: "amber",
      },
      {
        title: "Approved",
        value: approved?.count?.toString() || "0",
        icon: "Check",
        color: "green",
      },
      {
        title: "Rejected",
        value: rejected?.count?.toString() || "0",
        icon: "AlertTriangle",
        color: "red",
      },
    ];
  }

  async getTransactions(clientId?: number): Promise<Transaction[]> {
    if (clientId) {
      return await db.select().from(transactions).where(eq(transactions.client_id, clientId)).orderBy(desc(transactions.date));
    }
    return await db.select().from(transactions).orderBy(desc(transactions.date));
  }

  async getLoans(clientId?: number): Promise<Loan[]> {
    if (clientId) {
      return await db.select().from(loans).where(eq(loans.client_id, clientId)).orderBy(desc(loans.date));
    }
    return await db.select().from(loans).orderBy(desc(loans.date));
  }

  async getPointOfSales(clientId?: number): Promise<PointOfSale[]> {
    if (clientId) {
      return await db.select().from(pointOfSales).where(eq(pointOfSales.client_id, clientId)).orderBy(desc(pointOfSales.date));
    }
    return await db.select().from(pointOfSales).orderBy(desc(pointOfSales.date));
  }

  async getDashboardStats(): Promise<{ title: string; value: string; change: string; trend: string; icon: string; color: string; }[]> {
    const [totalClients] = await db.select({ count: count() }).from(clients);
    const [activeRequests] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Under Review'));
    const [approvedRequests] = await db.select({ count: count() }).from(requests).where(eq(requests.status, 'Approved'));

    return [
      {
        title: "Total Clients",
        value: totalClients?.count?.toString() || "0",
        change: "+12%",
        trend: "up",
        icon: "Users",
        color: "blue",
      },
      {
        title: "Active Requests",
        value: activeRequests?.count?.toString() || "0",
        change: "-3%",
        trend: "down",
        icon: "ClipboardList",
        color: "amber",
      },
      {
        title: "Approved Requests",
        value: approvedRequests?.count?.toString() || "0",
        change: "+8%",
        trend: "up",
        icon: "CheckCircle",
        color: "green",
      },
      {
        title: "Finance Given",
        value: "KWD 54,290", // This would need to be calculated from actual amounts
        change: "+15%",
        trend: "up",
        icon: "DollarSign",
        color: "purple",
      },
    ];
  }
}

// Use PostgreSQL storage if DATABASE_URL is provided, otherwise fallback to memory storage
export const storage = process.env.DATABASE_URL
  ? new PostgreSQLStorage()
  : new MemStorage();
